name = "tarot-seo"
main = "src/index.ts"
compatibility_date = "2023-12-01"

# 环境变量配置
[vars]
ENVIRONMENT = "development"

# D1数据库配置
# 统一数据库：开发和生产环境共用
[[d1_databases]]
binding = "DB" # 在Worker中通过env.DB访问
database_name = "tarot-seo-db"
database_id = "87945106-1e0d-4375-967c-d89324987198"
location = "enam" # 东北美地区，全球连接性最佳

# 开发环境配置
[env.development]
vars = { ENVIRONMENT = "development" }

# 生产环境配置
[env.production]
vars = { ENVIRONMENT = "production" }

# 缓存配置
[site]
bucket = "./public"

# 构建配置
[build]
command = "npm run build"
