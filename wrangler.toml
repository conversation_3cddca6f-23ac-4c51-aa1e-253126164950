name = "tarot-seo"
main = "src/index.ts"
compatibility_date = "2023-12-01"

# 环境变量配置
[vars]
ENVIRONMENT = "development"

# D1数据库配置
# 主数据库位置：亚太地区（适合中文用户群体）
[[d1_databases]]
binding = "DB" # 在Worker中通过env.DB访问
database_name = "tarot-seo-db"
database_id = "YOUR_DATABASE_ID" # 创建数据库后替换为实际ID
location = "apac" # 亚太地区，适合中文、日文、韩文用户

# 开发环境配置
[env.development]
vars = { ENVIRONMENT = "development" }

[[env.development.d1_databases]]
binding = "DB"
database_name = "tarot-seo-db-dev"
database_id = "YOUR_DEV_DATABASE_ID" # 开发环境数据库ID

# 生产环境配置
[env.production]
vars = { ENVIRONMENT = "production" }

[[env.production.d1_databases]]
binding = "DB"
database_name = "tarot-seo-db-prod"
database_id = "YOUR_PROD_DATABASE_ID" # 生产环境数据库ID

# 缓存配置
[site]
bucket = "./public"

# 构建配置
[build]
command = "npm run build"
