{"name": "mystical-website", "version": "1.0.0", "private": true, "description": "专业的多语言玄学网站 - 塔罗、星座、数字命理AI智能分析平台", "keywords": ["玄学", "塔罗牌", "星座", "数字命理", "AI分析", "多语言", "SEO"], "author": "Mystical Website Team", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate deploy", "db:seed": "tsx prisma/seed.ts", "d1:create": "wrangler d1 create tarot-seo-db --location=enam", "d1:local": "wrangler d1 execute tarot-seo-db --local --file=./prisma/schema.sql", "d1:remote": "wrangler d1 execute tarot-seo-db --remote --file=./prisma/schema.sql", "d1:migrate:local": "wrangler d1 migrations apply tarot-seo-db --local", "d1:migrate:remote": "wrangler d1 migrations apply tarot-seo-db --remote", "d1:backup": "wrangler d1 export tarot-seo-db --output=./backups/", "analyze": "cross-env ANALYZE=true next build", "postbuild": "next-sitemap", "prepare": "husky install"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@prisma/client": "^5.20.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.2", "@sentry/nextjs": "^8.33.1", "@upstash/redis": "^1.34.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.11.1", "lucide-react": "^0.446.0", "next": "^14.2.15", "next-intl": "^3.20.0", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "sharp": "^0.33.5", "tailwind-merge": "^2.5.2", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.15", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@types/node": "^22.7.4", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-next": "^14.2.15", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "prisma": "^5.20.0", "tailwindcss": "^3.4.13", "wrangler": "^3.78.12", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.1", "typescript": "^5.6.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}